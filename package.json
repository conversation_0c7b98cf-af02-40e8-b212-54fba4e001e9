{"name": "zqy-collaboration", "private": true, "version": "2.0.20", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "preview": "vite preview", "lint": "eslint --ext .js,.vue,.ts,.tsx,.d.ts src .", "lint:fix": "eslint --ext .js,.vue,.ts,.tsx,.d.ts src . --fix", "prepare": "husky", "dev:local": "vite --mode dev"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-jsapi-types": "^0.0.15", "@element-plus/icons-vue": "^2.0.10", "axios": "^1.7.2", "echarts": "^5.5.1", "element-plus": "2.9.6", "lodash": "^4.17.21", "moment": "^2.29.4", "qs": "^6.14.0", "quill": "^2.0.3", "vite": "^6.2.1", "vue": "^3.5.13", "vue-qr": "^4.0.9", "vue-router": "^4.4.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/lodash": "^4.17.16", "@types/node": "^22.13.10", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "3.5.3", "sass-embedded": "^1.86.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}, "overrides": {"rollup": "4.44.1"}, "resolutions": {"rollup": "4.44.1"}}