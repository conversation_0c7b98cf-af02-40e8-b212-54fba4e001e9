<template>
    <div v-if="channelType === 1" class="font-14">
        <div v-if="data[pro.list[1].model]">
            <div v-if="data[pro.list[1].model] > 1" class="pointer" style="color: #509de5"
                 @click="handlerOpenRelate(data)">
                {{ data[pro.list[0].model] }}【关联{{ data[pro.list[1].model] }}家企业】
            </div>

            <!-- 
										股东信息-Shareholder
										在股东信息中通过pid和关联数量来判断是否可以点击
										默认pid存在即为企业,反之
										如果存在pid 那么点击查看企业详情
										如果pid不存在 并且有关联数 可以点击查关联关系
										如果pid不存在 并且关联数小于2 不可点击 *注: 股东只关联一家企业的情况但是 返回的关联数量是'1' || '0' 
									-->
            <div v-else-if="modelName == 'Shareholder'">
                <div v-if="data.pid" class="pointer" style="color: #509de5"
                     @click="getCompanyDetail(data[pro.list[0].model])">
                    {{ data[pro.list[0].model] }}
                </div>
                <div v-if="!data.pid && data[pro.list[1].model] * 1 < 2">
                    {{ data[pro.list[0].model] }}
                </div>
            </div>
            <!-- 
										如果是对外投资或主要人员
										关联关系数量是0
										一律不可点击
									-->
            <div v-else-if="modelName == 'Investment' || modelName == 'KeyPerson'">
                <div>
                    {{ data[pro.list[0].model] }}
                </div>
            </div>
            <!-- 如果关联数是0  那么说明是企业 那么可以点击  -->
            <div v-else-if="(data[pro.list[1].model] * 1 == 0)" class="pointer" style="color: #509de5"
                 @click="getCompanyDetail(data[pro.list[0].model])">
                {{ data[pro.list[0].model] }}
            </div>

            <div v-else>
                {{ data[pro.list[0].model] }}
            </div>
        </div>
        <div v-else>
            <div class="pointer" style="color: #509de5" @click="getCompanyDetail(data[pro.list[0].model])">
                {{ data[pro.list[0].model] }}
            </div>
        </div>
    </div>

    <div v-if="channelType === 2">
        <div v-if="data[pro.list[1].model]">
            <div v-if="data[pro.list[1].model] > 0" class="pointer" style="color: #509de5"
                 @click="handlerOpenRelate(data)">
                {{ data[pro.list[0].model] }}
            </div>

            <div v-else-if="data[pro.list[1].model] == 0 && data.pid" class="pointer" style="color: #509de5"
                 @click="getCompanyDetail(data[pro.list[0].model])">
                {{ data[pro.list[0].model] }}
            </div>

            <div v-else>
                {{ data[pro.list[0].model] }}
            </div>
        </div>
        <div v-else>
            <div class="pointer" style="color: #509de5" @click="dealPersonOrCompany(data)">
                {{ data[pro.list[0].model] }}
            </div>
        </div>
    </div>
    <relateDialog v-if='relateCompanyVisible' v-model:dialogVisable="relateCompanyVisible" :relateInfo="relateInfo" />
</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps } from 'vue'
import type { Ref } from 'vue'

import { ElMessage } from 'element-plus'
import aicService from '@/service/aicService'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
    data: {
        type: Object,
        default: () => { }
    },
    channelType: {
        type: Number,
        default: 0
    },
    pro: {
        type: Object,
        default: () => { }
    },
    modelName: {
        type: String,
        default: ''
    }

})

const relateCompanyVisible: Ref<boolean> = ref(false)

const getCompanyDetail = (companyName: string = '') => {
    if (!companyName) {
        ElMessage({
            message: '数据错误',
            type: 'error',
        })
        return
    }
    aicService.searchEnterprise({
        keyword: companyName,
        scope: 'companyname',
        pageSize: 1,
        page: 1
    }).then((res) => {
        console.log(res)
        let company = res.data[0]
        if (company.companyName === companyName) {
            const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: company.socialCreditCode } }).href
            window.open(routeUrl, '_blank')
        } else {
            ElMessage({
                message: '未找到该公司',
                type: 'error',
            })
        }
    }).finally(() => { })
}

interface Relate {
    frpid?: string, pid?: string, INV?: string, LEGALPERSON?: string, name?: string, companyName?: string
}
const dealPersonOrCompany = (row: Relate) => {
    console.log('rerow', row)
    if (row.frpid) {
        // 存在frpid 指定为小蓝本对外投资法定代表人
        row.pid = row.frpid
        handlerOpenRelate(row)
    } else {
        let pid = row.pid || ''
        const distinguishRes = (pid).startsWith('p_')
        if (distinguishRes) {
            // 查不到企业信息 ==> 人
            handlerOpenRelate(row)
        } else {
            // 查到企业信息
            console.log(row)
            getCompanyDetail(row.INV)
        }
    }
}

const relateInfo = ref({})

const handlerOpenRelate = (data: Relate) => {
    console.log(data)

    relateInfo.value = {
        ...data,
        legalperson: data.INV || data.LEGALPERSON || data.name
    }
    relateCompanyVisible.value = true
}
onMounted(() => {
    console.log(props.data)
})

</script>

<style lang='scss' scoped></style>