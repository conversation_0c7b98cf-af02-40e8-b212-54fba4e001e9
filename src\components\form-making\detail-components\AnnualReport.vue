<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        @close="handleClose()"
        width="60%"
    >
        <el-scrollbar height="400px">
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">基本信息</span>
                <el-descriptions
                    class="t-margin-12"
                    :column="2"
                    border
                >
                    <el-descriptions-item
                        label="注册号/统一社会信用代码"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.regNo || baseInfo.uniscId ||'-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业名称"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.entName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业通讯地址"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.addr || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="邮政编码"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.postalCode || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业联系电话"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.tel || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业电子邮箱"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.email || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="从业人数"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.empNum || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="其中女性从业人数"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.womemPNum || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业营业状态"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.busSt_CN || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业控股情况"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.ammontCount || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="是否有投资信息或购买其他公司股权"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.guaranteeInfo || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="是否有网站或网店"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.hasWebsiteOnlineshop || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="是否有对外提供担保信息"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.hasNewStockOrByStock || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="有限责任公司本年度是否发生股东股权转让"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.hasStockTransfer || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="企业主营业务活动"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ baseInfo.businessScope || '-' }}
                    </el-descriptions-item>

                </el-descriptions>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">网站网店信息</span>
                <el-table
                    class="t-margin-12"
                    :data="website"
                    empty-text="暂无数据"
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="类型"
                        prop="webType"
                    />
                    <el-table-column
                        label="网店名称"
                        prop="webSiteName"
                    />
                    <el-table-column
                        label="网址"
                        prop="domain"
                    />
                </el-table>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">股东及出资信息</span>
                <el-table
                    class="t-margin-12"
                    :data="sponsor"
                    v-loading="sponsorTableLoading"
                    empty-text="暂无数据"
                    show-overflow-tooltip
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="发起人"
                        prop="invName"
                    />
                    <el-table-column
                        label="认缴金额"
                        prop="liSubConAm"
                    />
                    <el-table-column
                        label="认缴日期"
                        prop="subConDate"
                        min-width="120"
                    >
                        <template #default="scope">
                            {{
                                scope.row.subConDate
                                    ? moment(scope.row.subConDate).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </template>    
                    </el-table-column>
                    <el-table-column
                        label="认缴出资方式"
                        prop="subConFormName"
                        min-width="120"
                    />
                    <el-table-column
                        label="实缴金额"
                        prop="liAcConAm"
                    />
                    <el-table-column
                        label="实缴日期"
                        prop="acConDate"
                        min-width="120"
                    >
                        <template #default="scope">
                            {{
                                scope.row.acConDate
                                    ? moment(scope.row.acConDate).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </template>    
                    </el-table-column>
                    <el-table-column
                        label="实缴出资方式"
                        prop="acConFormName"
                        min-width="120"
                    />
                </el-table>
                <!-- 分页器 -->
                <div class="display-flex justify-end">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, prev, pager, next"
                        @change="pageChange"
                    />
                </div>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">对外投资信息</span>
                <el-table
                    class="t-margin-12"
                    :data="investment"
                    empty-text="暂无数据"
                    show-overflow-tooltip
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="投资设立企业或购买股权企业名称"
                        prop="entName"
                    />
                    <el-table-column
                        label="注册号/统一社会信用代码"
                        prop="uniscId"
                    />
                </el-table>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">企业资产状况信息</span>
                <el-descriptions
                    class="t-margin-12"
                    :column="2"
                    border
                >
                    <el-descriptions-item
                        label="资产总额"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.assGro || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="所有者权益合计"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.totEqu || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="营业总收入"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="利润总额"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.netInc || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="营业总收入中主营业务收入"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.maiBusInc || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="净利润"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.proGro || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="纳税总额"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.ratGro || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="负债总额"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ assetInfo.liaGro || '-' }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">对外提供保证担保信息</span>
                <el-table
                    class="t-margin-12"
                    :data="guarantee"
                    empty-text="暂无数据"
                    show-overflow-tooltip
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="债权人"
                        prop="1"
                    />
                    <el-table-column
                        label="债务人"
                        prop="2"
                    />
                    <el-table-column
                        label="主债权种类"
                        prop="3"
                    />
                    <el-table-column
                        label="主债券数额"
                        prop="4"
                    />
                    <el-table-column
                        label="履行债务的期限"
                        prop="5"
                    />
                    <el-table-column
                        label="保证的期间"
                        prop="6"
                    />
                    <el-table-column
                        label="保证的方式"
                        prop="7"
                    />
                </el-table>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">股权变更信息</span>
                <el-table
                    class="t-margin-12"
                    :data="alterStock"
                    empty-text="暂无数据"
                    show-overflow-tooltip
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="股东"
                        prop="inv"
                    />
                    <el-table-column
                        label="变更前股权比例"
                        prop="transAmPr"
                    />
                    <el-table-column
                        label="变更后股权比例"
                        prop="transAmAft"
                    />
                    <el-table-column
                        label="股权变更日期"
                        prop="altDate"
                    >
                        <template #default="scope">
                            {{
                                scope.row.altDate
                                    ? moment(scope.row.altDate).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </template>    
                    </el-table-column>
                </el-table>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">社保信息</span>
                <el-descriptions
                    class="t-margin-12"
                    border
                >
                    <el-descriptions-item
                        label="城镇职工基本养老保险"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ annSocsecinfo.so110 || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="职工基本医疗保险"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ annSocsecinfo.so210 || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="生育保险"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ annSocsecinfo.so510 || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="失业保险"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ annSocsecinfo.so310 || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="工伤保险"
                        label-align="left"
                        align="center"
                        width="150"
                    >
                        {{ annSocsecinfo.so410 || '-' }}
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                    border
                >
                    <el-descriptions-item
                        label="单位缴费基数"
                        label-align="left"
                        align="center"
                        width="100"
                    >
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加城镇职工基本养老保险缴费基数</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加失业保险缴费基数</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加职工基本医疗保险缴费基数</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加生育保险缴费基数</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                    border
                >
                    <el-descriptions-item
                        label="本期实际缴费金额"
                        label-align="left"
                        align="center"
                        width="100"
                    >
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加城镇职工基本养老保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加失业保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加职工基本医疗保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加工伤保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加生育保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                    border
                >
                    <el-descriptions-item
                        label="单位累计欠缴金额"
                        label-align="left"
                        align="center"
                        width="100"
                    >
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加城镇职工基本养老保险累计欠缴金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加失业保险累计欠缴金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">参加职工基本医疗保险本期实际缴费金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加工伤保险累计欠缴金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        <div class="display-flex space-between" style="height: 50px;">
                            <div class="flex-center" style="width: 50%;">单位参加生育保险累计欠缴金额</div>
                            <div class="flex-center" style="width: 50%;">-</div>
                        </div>
                        
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="b-margin-12">
                <span class="font-16 font-weight-600">修改信息</span>
                <el-table
                    class="t-margin-12"
                    :data="alter"
                    empty-text="暂无数据"
                    show-overflow-tooltip
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column
                        label="修改事项"
                        prop="1"
                    />
                    <el-table-column
                        label="修改前"
                        prop="2"
                    />
                    <el-table-column
                        label="修改后"
                        prop="3"
                    />
                    <el-table-column
                        label="修改日期"
                        prop="altDate"
                    >
                        <template #default="scope">
                            {{
                                scope.row.altDate
                                    ? moment(scope.row.altDate).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </template>    
                    </el-table-column>
                </el-table>
            </div>
        </el-scrollbar>

        <div class="display-flex flex-end t-margin-12">
            <button class="close-button" @click="handleClose()">关闭</button>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, defineProps, watch, getCurrentInstance, reactive } from 'vue'
import aicService from '@/service/aicService'
import { ElMessage } from 'element-plus'

const sponsorTableLoading = ref<boolean>(false)
let pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
const pageChange = (currentPage: number,identifier:string) => {
    console.log('currentPage',currentPage)
    console.log('identifier',identifier)
    // queryParams.page = currentPage
    // queryParams.pageSize = pageSize
    // search()
}

const search = () => {
    sponsorTableLoading.value = true
    let queryParams = {
        annualReportId:props.row.annualReportId,
        label: '',
        page: pageInfo.page,
        pageSize: pageInfo.pageSize,
    }
}

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

interface IAnnualReportParams {
    address:string,
    annualReportId:string,
    publicshDate:string,
    reportYear:string,
    socialSecNum:string
}
const props = defineProps<{
    visible: boolean;
    row:IAnnualReportParams;
}>()
const dialogVisible = ref<boolean>(props.visible)
// 基础信息
const baseInfo = ref({})
// 网站信息
const website = ref([])
// 股东及出资信息
const sponsor = ref([])
// 对外投资信息
const investment = ref([])
// 企业资产状况信息
const assetInfo = ref({})
// 对外提供保证担保信息
const guarantee = ref([])
// 股权变更信息
const alterStock = ref([])
// 社保信息
const annSocsecinfo = ref({})
// 修改信息
const alter = ref([])
const dialogTitle = ref('年度报告')
const getAnnualReport = async() => {
    console.log('row',props.row)
    if(!props.row.annualReportId){
        ElMessage.error('暂无数据')
        return
    }
    dialogTitle.value = `${props.row.reportYear}年度报告`
    const res = await aicService.getAnnualReportDetail({annualReportId:props.row.annualReportId})
    console.log('baseInfo',res)
    if(res.success && res.data){
        baseInfo.value = res.baseInfo || {}
        website.value = res.website || {}
        sponsor.value = res.sponsor.items || []
        pageInfo.total = res.sponsor.total || 0
        investment.value = res.investment.items || []
        assetInfo.value = res.assetInfo || {}
        guarantee.value = res.guarantee.items || []
        alterStock.value = res.alterStock.items || []
        annSocsecinfo.value = res.annSocsecinfo || {}
        alter.value = res.alter.items || []
    }

    
}

watch(() => props.visible, async (val) => {
    dialogVisible.value = val
    if (val) {
        await getAnnualReport()
    }
}, { immediate: true })

const emit = defineEmits(['update:visible'])
const handleClose = () => {
    dialogVisible.value = false
    emit('update:visible', false)
}

</script>

<style lang='scss' scoped>

.close-button {
    background-color: #f5f5f5; 
    color: #333; 
    border: 1px solid #ccc; 
    padding: 10px 20px; 
    border-radius: 4px;
    cursor: pointer; 
}

</style>