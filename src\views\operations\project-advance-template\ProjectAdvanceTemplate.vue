<template>
    <div class="display-flex flex-column">
        <div class="b-margin-16 back-color-white all-padding-16 search-box">
            <SearchBox :searchOptionKey="'ADVANCE_TEMPLETE_SEARCH_OPTIONS'" @updateSearchParams="updateSearchParams">
            </SearchBox>
        </div>
        <div class="back-color-white all-padding-16 felx-1">
            <div class="display-flex justify-flex-end b-margin-16">
                <el-button type="primary" @click="addTemplate">新增</el-button>
            </div>
            <el-table ref="tableListRef" :data="templateList" row-key="id">
                <el-table-column prop="name" label="模板名称" width="400"></el-table-column>
                <el-table-column prop="categoryNames" label="所属分类">
                    <template #default="scope">
                        {{ scope.row.categoryNames.join() }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="left" width="200">
                    <template #default="scope">
                        <el-button style="font-size: 16px;padding-left: 0;" type="primary" text
                            @click="editTemplate(scope.row)">编辑</el-button>
                        <el-button type="primary" text @click="deleteTemplate(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="page-box display-flex justify-flex-end tb-padding-10">
                <el-pagination @change="search()" v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]" v-model:current-page="pageInfo.page"
                    layout="total,sizes,prev, pager, next,jumper" :total="pageInfo.total" />
            </div>
        </div>
    </div>

    <el-dialog v-model="editTemplateDialogFlag" title="编辑模板">
        <div class="lr-padding-16">
            <el-form ref="ruleFormRef" :model="templateItem" :label-position="'top'" :rules="rules" label-width="auto">
                <el-form-item label="模板名称:" prop="name">
                    <el-input v-model="templateItem.name"></el-input>
                </el-form-item>
                <el-form-item label="所属分类:" prop="categoryIds">
                    <el-select v-model="templateItem.categoryIds" placeholder="选择所属分类" style="width: 240px" filterable
                        multiple remote remote-show-suffix :remote-method="categoryFilter">
                        <el-option v-for="item in temCategoryList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>


            <HighSearchRules v-if="editTemplateDialogFlag" ref="highSearchRule" @search="search"
                :activeTemplete="templateItem" :isAdmin="true" />
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="editTemplateDialogFlag = false">取消</el-button>
                <el-button type="primary" @click="saveTemplete(ruleFormRef)">
                    保存
                </el-button>
            </div>
        </template>

    </el-dialog>


</template>

<script lang='ts' setup>
import { ref, onMounted, reactive } from 'vue'
import type { Ref } from 'vue'

import type { FormInstance, FormRules } from 'element-plus'

import { ElMessageBox, ElMessage } from 'element-plus'

import type {
    ISearchGetTemplateParams,
    ISearchGetTemplateItem,
    ISearchGetTemplateResponse,
    ISearchGetCategoryItem
} from '@/types/company'


import type { ISearchConditions } from '@/types/model'

import aicService from '@/service/aicService'

import SearchBox from '@/components/common/SearchBox.vue'
import HighSearchRules from '@/components/search/HighSearchRules.vue'

interface RuleForm {
    name: string
    categoryIds: string[]
}
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules<RuleForm>>({
    name: [
        { required: true, message: '请输入模板名称', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    categoryIds: [
        { required: true, message: '请选择模板分类', trigger: 'change' }
    ]
})


const pageInfo = reactive<{ page: number, pageSize: number, total: number }>({
    page: 1,
    pageSize: 20,
    total: 0,
})

const templateList = ref<ISearchGetTemplateItem[]>([])

const searchParams: Ref<ISearchGetTemplateParams> = ref({} as ISearchGetTemplateParams)
const search = async () => {
    aicService.searchGetTemplate({
        searchType: '1',
        ...searchParams.value,
        ...pageInfo
    }).then((res: ISearchGetTemplateResponse) => {
        templateList.value = res.data
        pageInfo.total = res.total
    })
}

const allCategoryList: Ref<ISearchGetCategoryItem[]> = ref([])

const temCategoryList: Ref<ISearchGetCategoryItem[]> = ref([])



const categoryFilter = (query: string) => {

    console.log(query)
    if (query) {
        temCategoryList.value = allCategoryList.value.filter((item: ISearchGetCategoryItem) => item?.name?.includes(query)).splice(0, 100)
    } else {
        temCategoryList.value = allCategoryList.value.filter((item: ISearchGetCategoryItem) => templateItem.value.categoryIds.includes(item.id))

    }
}

const getAllCategoryList = async () => {
    aicService.modelLoadCategory({}).then((res) => {
        console.log(res)
        allCategoryList.value = res.data

    })
}
const updateSearchParams = (params: ISearchGetTemplateParams) => {
    searchParams.value = params
    search()
}

const templateItem: Ref<ISearchGetTemplateItem> = ref({} as ISearchGetTemplateItem)

const editTemplateDialogFlag: Ref<boolean> = ref(false)

const addTemplate = () => {
    templateItem.value = {} as ISearchGetTemplateItem
    editTemplateDialogFlag.value = true
    temCategoryList.value = allCategoryList.value.filter((item: ISearchGetCategoryItem) => templateItem.value.categoryIds.includes(item.id))

}

const editTemplate = (item: ISearchGetTemplateItem) => {

    editTemplateDialogFlag.value = true
    templateItem.value = item
    temCategoryList.value = allCategoryList.value.filter((item: ISearchGetCategoryItem) => templateItem.value.categoryIds.includes(item.id))

}

const highSearchRule = ref<{ searchConditions: ISearchConditions }>({} as { searchConditions: ISearchConditions })


const deleteTemplate = (item: ISearchGetTemplateItem) => {

    ElMessageBox.confirm('是否确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        aicService.searchDeleteTemplate(item.id).then(() => {
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
            search()
        })
    })
}

const saveTemplete = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            if (templateItem.value.id) {
                //编辑
                aicService.searchUpdateTemplate({
                    templateId: templateItem.value.id,
                    name: templateItem.value.name,
                    categoryIds: templateItem.value.categoryIds,
                    searchData: {
                        list: [highSearchRule.value.searchConditions]
                    }
                }).then(() => {
                    ElMessage({
                        type: 'success',
                        message: '保存成功',
                    })
                    editTemplateDialogFlag.value = false
                    search()
                })
            } else {
                aicService.searchSaveTemplate({
                    name: templateItem.value.name,
                    categoryIds: templateItem.value.categoryIds,
                    searchData: {
                        list: [highSearchRule.value.searchConditions]
                    }
                }).then(() => {
                    ElMessage({
                        type: 'success',
                        message: '保存成功',
                    })
                    editTemplateDialogFlag.value = false
                    search()
                })

            }

        } else {
            console.log('error submit!', fields)
        }
    })
}

onMounted(() => {
    search()
    getAllCategoryList()
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.search-box {}
</style>