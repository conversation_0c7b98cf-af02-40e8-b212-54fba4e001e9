<template>
    <el-dialog
        v-model="showDialog"
        title="写跟进"
        width="520px"
        @close="handleClose"
    >   
        <div class="b-margin-10 lr-padding-10 tb-padding-6 display-flex top-bottom-center space-between" v-if="cacheData" style="width: 98%;height: 30px;background-color: #FFF6EA;">
            <view class="padding-right-20 display-flex color-two-grey">
                <el-icon color="#FFA82F" size="16px" class="r-margin-6"><WarningFilled /></el-icon>
                自动保存于
                {{ moment(parseInt(cacheData.date)).format("YYYY-MM-DD HH:mm:ss") }}
                的草稿，是否
                <span @click="rebackInfo" class="lr-margin-10 color-primary pointer">恢复 </span> ？
            </view>
            <el-icon class="pointer" size="16px" color="#979797" @click="clearCache"><Close /></el-icon>
        </div>
        <el-scrollbar height="378px">
            <el-form 
                ref="form" 
                v-model="formData" 
                validateTrigger="bind"
                label-position="top"
            >   
                <el-form-item name="name" label="跟进企业名称">
                    <el-input v-model="formData.name" disabled style="width: 98%;"></el-input>
                </el-form-item>
                <el-form-item name="phone" label="跟进方式">
                    <el-select v-model="formData.followType" style="width: 98%;">
                        <el-option label="电话" value="电话"></el-option>
                        <el-option label="微信" value="微信"></el-option>
                        <el-option label="短信" value="短信"></el-option>
                        <el-option label="拜访" value="拜访"></el-option>
                        <el-option label="面谈" value="面谈"></el-option>
                        <el-option label="其他" value="其他"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item name="realFollowDate" label="实际跟进时间"> 
                    <el-date-picker
                        v-model="formData.realFollowDate"
                        value-format="x"
                        @change="dateChange"
                        type="datetime"
                        range-separator="-"
                        clearable
                    />
                </el-form-item>
                <el-form-item name="" label="跟进内容" required>
                    <view
                        class="border-radius-6 com-padding-10"
                    >
                        <textarea
                            :maxlength="-1"
                            class="font-14"
                            style="max-width: 478px;height: 118px; width: 478px;"
                            v-model="formData.description"
                            clearable
                            placeholder="勤跟进,多签单..."
                        ></textarea>
                        
                        <view class="t-margin-12 top-bottom-center">
                            <el-upload
                                ref="uploadImgRef"
                                class="upload-demo"
                                :action="uploadFile"
                                :headers="headers"
                                v-model:file-list="PictureList"
                                accept="image/jpeg,image/png,image/jpg"
                                :on-success="onImgSuccess"
                                :on-change="onImgChange"
                                :on-remove="onImgRemove"
                                :on-error="onError"
                                list-type="picture"
                            >
                                <view
                                    class="fon-14 display-flex top-bottom-center r-margin-10 pointer"
                                    @click="chooseImg"
                                >
                                    <Icon icon="icon-a-huaban71" size="18"></Icon>
                                    <view class="l-margin-10">图片</view>
                                </view>
                            </el-upload>
                            <el-upload
                                ref="uploadFileRef"
                                v-model:file-list="FileList"
                                class="upload-demo"
                                :action="uploadFile"
                                :headers="headers"
                                :on-success="onFileSuccess"
                                :on-change="onFileChange"
                                :on-remove="onFileRemove"
                                :on-error="onError"
                                accept=".doc,.docx,.xlsx,.xls,.pdf"
                            >
                                <view
                                    class="fon-14 display-flex top-bottom-center r-margin-10 pointer"
                                    @click="chooseFile"
                                >
                                    <Icon icon="icon-a-huaban84" size="18"></Icon>
                                    <view class="l-margin-10">附件</view>
                                </view>

                            </el-upload>
                        </view>
                    </view>
                </el-form-item>
                <el-form-item name="nextFollowDate" label="下次跟进时间">
                    <el-date-picker
                        v-model="formData.nextFollowDate"
                        value-format="x"
                        @change="dateChange"
                        type="datetime"
                        range-separator="-"
                        clearable
                    />
                </el-form-item>
                
            </el-form>
        </el-scrollbar>
        <view class="dialog-footer">
            <el-button
                style="width: 64px; height:40px"
                class="l-margin--12"
                @click="closeAddSalesDynamics"
            >取消</el-button>
            <el-button
                :loading="loading"
                type="primary"
                style="width: 64px; height:40px"
                @click="submitForm"
            >提交</el-button>
        </view>
    </el-dialog>

</template>

<script lang='ts' setup>
import { getCurrentInstance, ref, onMounted } from 'vue'
import type {ICrmGetActiviesItem, CacheData, FollowImg, FollowFiles, FileUploadResponse, FileIns} from '@/types/lead'
import { ElMessage } from 'element-plus'
import type { UploadUserFile, ElUpload, UploadStatus } from 'element-plus'
import crmService from '@/service/crmService'
import Icon from '@/components/common/Icon.vue'
import fileService from '@/service/fileService'

const uploadImgRef = ref<InstanceType<typeof ElUpload> | null>(null)
const uploadFileRef = ref<InstanceType<typeof ElUpload> | null>(null)
const PictureList = ref<UploadUserFile[] | FollowImg[]>([])
const FileList = ref<UploadUserFile[] | FollowFiles[]>([])
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
// 存储正在上传的图片/文件
const uploadingImg = ref<FileIns[]>([])
const uploadingFile = ref<FileIns[]>([])

type CrmList= { 
    id: string,
    name: string,
}

const props = defineProps({
    showAddSalesDynamics: {
        type: Boolean,
        required: true,
    },
    permission: {
        type: String,
        required: true,
    },
    crmItem: {
        type: Object,
        required: true,
    },
})
const formData = ref<ICrmGetActiviesItem>({
    description: '',
    followType: '',
    nextFollowDate: '',
    realFollowDate: '',
    followImg: [],
    followFiles: [],
    createTime: '',
    activityType: '',
    leadId: '',
    customerId: '',
    referName: '',
    referType: '',
})
const loading = ref(false)
const crmList = ref<CrmList[]>([])
const cacheData = ref<CacheData | null>(null)
const showDialog = ref(false)
const noSaveFlag = ref(false)

showDialog.value = props.showAddSalesDynamics
const emit = defineEmits(['closeAddSalesDynamics', 'getSalesList', 'refreshData','getSalesDynamicsList'])

//终止还未上传成功的文件 
const abortUpload = () => {
    const uploadImgInstance = uploadImgRef.value
    if (uploadImgInstance && uploadImgInstance.abort) {
        // console.log('abort')
        // console.log('uploadingImg',uploadingImg.value)
        for(let i=0 ; i < uploadingImg.value.length ; i++){
            // 调用 abort 方法
            const file = uploadingImg.value[i]
            uploadImgInstance.abort({uid:file.uid,name:file.name,status:file.status as UploadStatus})
        }
    }

    const uploadFileInstance = uploadFileRef.value
    if (uploadFileInstance && uploadFileInstance.abort) {
        // console.log('abort')
        // console.log('uploadingFile',uploadingFile.value)
        for(let i=0 ; i < uploadingFile.value.length ; i++){
            // 调用 abort 方法
            const file = uploadingFile.value[i]
            uploadFileInstance.abort({uid:file.uid,name:file.name,status:file.status as UploadStatus})
        }
    }
}

const rebackInfo = () => {
    formData.value = JSON.parse(JSON.stringify(cacheData.value?.data))
    // console.log('formData',formData.value)
    if(formData.value.followImg?.length){
        for(let i = 0 ; i < formData.value.followImg.length ; i++){
            PictureList.value.push({
                name: formData.value.followImg[i].originalName,
                url: fileService.getFileUrl(formData.value.followImg[i].name),
                originalName: formData.value.followImg[i].originalName,
            })
        }
    }
    if(formData.value.followFiles?.length){
        for(let i = 0 ; i < formData.value.followFiles.length ; i++){
            FileList.value.push({
                name: formData.value.followFiles[i].originalName,
                url: fileService.getFileUrl(formData.value.followFiles[i].name),
                originalName: formData.value.followFiles[i].originalName,
            })
        }
        
    }
    clearCache()
}
const clearCache = () => {
    localStorage.removeItem(`cachedSalesInfo:${props.crmItem.id}`)
    noSaveFlag.value = true
    cacheData.value = null
}
const dateChange = () =>{
}

const chooseImg = () => {
    // console.log('选择图片')
}

const chooseFile = () => {
    // console.log('选择文件')
}

const submitForm =async () => {
    // console.log('提交表单')
    // console.log('formData',formData.value)
    if(
        !formData.value.description &&
        formData.value.followFiles?.length === 0 &&
        formData.value.followImg?.length === 0
    ){
        ElMessage.error('请填写跟进内容')
        return
    }
    if(uploadingImg.value.length || uploadingFile.value.length){
        ElMessage.error(`${uploadingImg.value.length + uploadingFile.value.length}个文件未上传成功，请等待文件上传完成`)
    }else{
        try{
            loading.value = true
            let params = {
                activityType:'create',
                leadId: props.crmItem.id,
                followType: '',
                realFollowDate: '',
                nextFollowDate :'',
                description: '',
                followFiles: [] as FollowFiles[],
                followImg: [] as FollowImg[]
            }
            params.followType = formData.value.followType || ''
            // 实际跟进时间
            if(formData.value.realFollowDate){
                params.realFollowDate = formData.value.realFollowDate
            }
            // 下次跟进时间
            if(formData.value.nextFollowDate){
                params.nextFollowDate = formData.value.nextFollowDate
            }
            // 跟进内容
            if(formData.value.description){
                params.description = formData.value.description
            }
            // 文件
            if(formData.value.followFiles?.length){
                params.followFiles = formData.value.followFiles.map((i) => {
                    return {
                        name: i.name,
                        originalName: i.originalName,
                    }
                })}
            // 图片
            if(formData.value.followImg?.length){
                params.followImg = formData.value.followImg.map((i) => {
                    return {
                        name: i.name,
                        originalName: i.originalName,
                    }
                })
            }
            // console.log('params',params)
            const res = await crmService.crmAddActivies(params)
            // console.log('res',res)
            if(res.success){
                ElMessage.success('提交成功')
            }
            emit('getSalesDynamicsList',{page:1,pageSize:100,leadId:props.crmItem.id})
            loading.value = false
        }catch(error){
            ElMessage.error((error as { message: string }).message)
        }finally{
            loading.value = false
            emit('closeAddSalesDynamics',false) 
        }
    }  
}
const handleClose = () => {
    closeAddSalesDynamics()
}
const closeAddSalesDynamics = () => {
    emit('closeAddSalesDynamics',false)
    abortUpload()
    if(
        formData.value.description ||
        formData.value.followImg?.length ||
        formData.value.followFiles?.length
    ){
        let data = {
            date: new Date().getTime(),
            data: formData.value,
        }
        localStorage.setItem(
            `cachedSalesInfo:${props.crmItem.id}`,
            JSON.stringify(data)
        )    
    }
}

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}

const onImgSuccess = (file: FileUploadResponse) => {
    // console.log('fileList', fileList) 
    const fileUrl = fileService.getFileUrl(file.data.originalName)
    formData.value.followImg?.push( {name:file.data.name,url:fileUrl,originalName:file.data.originalName})
}

const onFileSuccess = (file: FileUploadResponse) => {
    // console.log('fileList', fileList)
    const fileUrl = fileService.getFileUrl(file.data.originalName)
    formData.value.followFiles?.push( {name : file.data.name,url:fileUrl,originalName:file.data.originalName})
}

// 上传图片事件
const onImgChange = (file: FileIns) => {
    console.log('on-change 事件触发：', file)
    if (file.status === 'ready'){
        // console.log('开始上传')
        uploadingImg.value.push(file)
        // console.log('uploadingImg', uploadingImg.value)
    }
    if (file.status === 'success') {
        // console.log('上传成功')
        ElMessage.success('上传成功')
        const index = uploadingImg.value.findIndex(uploadingImg => uploadingImg.uid === file.uid)
        if (index !== -1) {
            uploadingImg.value.splice(index, 1)
        }
        // console.log('uploadingImg', uploadingImg.value)
    } 
}
// 上传文件事件
const onFileChange = (file: FileIns) => {
    // console.log('on-change 事件触发：', file)
    if (file.status === 'ready'){
        // console.log('开始上传')
        uploadingFile.value.push(file)
        // console.log('uploadingFile', uploadingFile.value)
    }
    if (file.status === 'success') {
        // console.log('上传成功')
        ElMessage.success('上传成功')
        const index = uploadingFile.value.findIndex(uploadFile => uploadFile.uid === file.uid)
        if (index !== -1) {
            uploadingFile.value.splice(index, 1)
        }
        // console.log('uploadingFile', uploadingFile.value)
    } 
}

const onImgRemove = (file: FileIns) => {
    // console.log('fileList', fileList)
    // 删除formData中对应的文件
    uploadingImg.value = uploadingImg.value.filter(uploadingImg => uploadingImg.uid !== file.uid)
    console.log('uploadingImg', uploadingImg.value)
    const index = formData.value.followImg?.findIndex(item => item.url === file.response.data.link)
    if (index !== undefined && index >= 0) {
        formData.value.followImg?.splice(index, 1)
    }
    console.log('Updated formData:', formData.value)
}
const onFileRemove = (file: FileIns) => {
    // console.log('fileList', fileList)
    // 删除formData中对应的文件
    uploadingFile.value = uploadingFile.value.filter(uploadFile => uploadFile.uid !== file.uid)
    const index = formData.value.followFiles?.findIndex(item => item.url === file.response.data.link)
    if (index !== undefined && index >= 0) {
        formData.value.followFiles?.splice(index, 1)
    }
    console.log('Updated formData:', formData.value)
}

const onError = ( file: FileIns) => {
    console.log('onError 事件触发：', file)
    ElMessage.error('上传失败')
}

onMounted(() => {
    crmList.value.push(props.crmItem.id)
    formData.value.leadId = props.crmItem.id
    formData.value.name = props.crmItem.name
    const cachedValue = localStorage.getItem(`cachedSalesInfo:${props.crmItem.id}`)
    if (cachedValue) {
        // 手动解析并断言类型
        cacheData.value = JSON.parse(cachedValue) as CacheData 
    }
})

</script>

<style lang='scss' scoped>
.upload-demo{
    max-width: 480px;
}

.com-padding-10{
    padding: .1rem;
}

.uni-button {
	padding: 10px 20px;
	font-size: 14px;
	border-radius: 4px;
	line-height: 1;
	margin: 0;
	box-sizing: border-box;
	overflow: initial;
}

:deep(.el-upload-list__item-file-name){
    max-width: 350px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-top: 20px;
}

</style>