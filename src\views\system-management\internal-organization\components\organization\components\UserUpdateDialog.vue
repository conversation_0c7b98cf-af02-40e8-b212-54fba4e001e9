<script setup lang="ts">
import systemService from '@/service/systemService'
import type { IOrgTreeItem } from '@/types/org'
import type { IRoleItem, IRoleListRequest } from '@/types/role'
import type { RootState } from '@/types/store'
import type { IPageUserItem, IUserEditRequest } from '@/types/user'
import { flatten } from '@/utils/flatten'
// import { phoneValidate } from '@/utils/validate'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

const props = defineProps<{
    visible: boolean
    onClose: (refresh?: boolean) => void
    orgList: IOrgTreeItem[]
    type: string
    user: IPageUserItem | null
    currentOrg: IOrgTreeItem | null
}>()

const store = useStore<RootState>()
const dialogVisible = ref(false)
const formRef = ref<FormInstance | null>(null)
const loading = ref(false)
// const querying = ref(false)
// const countdown = ref(0) // 记录倒计时秒数
// const timer = ref<ReturnType<typeof setInterval> | null>(null)

const origin = {
    id: '',
    // mobile: '',
    nickname: '',
    orgId: '',
    password: '',
    role: [] as string[],
    tenantId: '',
    username: '',
    // smsCode: '',
    status: '0',
}

const form = ref(JSON.parse(JSON.stringify(origin)))
const queryingRole = ref(false)
const roleList = ref<IRoleItem[]>([])

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const handleClose = (refresh?: boolean) => {
    console.log('handleClose', refresh)
    dialogVisible.value = false
    props.onClose(refresh)
}

const resetForm = () => {
    if (!formRef.value) return
    formRef.value.resetFields()
    form.value = JSON.parse(JSON.stringify(origin))
}

const getTenantId = () => {
    if (!isPlatManager.value) return ''
    const { tenantId } = props.currentOrg || {}
    form.value.tenantId = tenantId
}

const addUser = async (formEle: FormInstance | null) => {
    if (loading.value) return
    if (!formEle) return
    loading.value = true
    await formEle.validate((valid) => {
        console.log('form.value',form.value)
        if (valid) {
            getTenantId()
            // 格式化 orgId 为数组
            const submitData = {
                ...form.value,
                orgId: form.value.orgId ? [form.value.orgId] : []
            }
            console.log('submitData', submitData)
            systemService
                .userAdd(submitData)
                .then((res) => {
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '添加失败')
                    } else {
                        ElMessage.success('添加成功')
                        handleClose(true)
                        resetForm()
                    }
                })
                .catch(() => {
                    ElMessage.error('添加失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const editUser = async (formEle: FormInstance | null) => {
    const { user } = props
    if (loading.value) return
    if (!formEle) return
    if (!user) return
    if (!user.id) return

    loading.value = true
    await formEle.validate((valid) => {
        if (valid) {
            // 格式化 orgId 为数组
            const params: IUserEditRequest = {
                ...form.value,
                id: user.id,
                orgId: form.value.orgId ? [form.value.orgId] : []
            }

            if (form.value.password !== '********') {
                params.updatePwd = {
                    newPassword: form.value.password,
                }
            }

            // if (form.mobile !== user.mobile) {
            //     params.authMobileDto = {
            //         code: form.smsCode,
            //         mobile: form.mobile,
            //     }
            // }

            console.log('editUser params', params)
            systemService
                .userEdit(params)
                .then((res) => {
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '操作失败')
                    } else {
                        ElMessage.success(props.type === 'add' ? '添加成功' : '编辑成功')
                        handleClose(true)
                        resetForm()
                    }
                })
                .catch((err) => {
                    ElMessage.error(err || '操作失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const save = (formEle: FormInstance | null) => {
    if (props.type === 'add') {
        addUser(formEle)
    }
    if (props.type === 'edit') {
        editUser(formEle)
    }
}

// const isShowSmscode = computed(() => {
//     return (props.type === 'edit' && form.mobile !== props.user?.mobile) || props.type === 'add'
// })

const rules = reactive<FormRules<typeof form>>({
    username: [
        { required: true, message: '请输入登录账号', trigger: 'change' },
        {
            validator: (_, value, callback) => {
                if (value.length < 6 || value.length > 12) {
                    callback(new Error('登录账号长度必须在6到12个字符之间'))
                } else if (!/^[a-zA-Z0-9]+$/.test(value)) {
                    // 仅允许字母和数字
                    callback(new Error('账号只能包含字母和数字'))
                } else {
                    callback()
                }
            },
            trigger: 'change',
        },
    ],
    password: [
        { required: true, message: '请输入登录密码', trigger: 'change' },
        {
            validator: (rule, value, callback) => {
                console.log(rule)
                if (!value || value === '********') {
                    // 如果为空，不做其他校验
                    callback()
                } else {
                    if (value.length < 6 || value.length > 16) {
                        callback(new Error('密码长度必须在6到16个字符之间'))
                    } else if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
                        callback(new Error('密码必须包含字母和数字'))
                    } else {
                        callback()
                    }
                }
            },
            trigger: 'change',
        },
    ],
    orgId: [{ required: true, message: '请选择所属组织', trigger: 'change' }],
    status: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
    role: [{ required: true, message: '请选择角色', trigger: 'change' }],
    nickname: [{ required: true, message: '请输入联系人姓名', trigger: 'change' }],
    // mobile: [
    //     { required: true, message: '请输入手机号', trigger: 'change' },
    //     {
    //         pattern: /^1[3-9]\d{9}$/,
    //         message: '请输入正确的手机号码',
    //         trigger: 'blur',
    //     },
    // ],
    // smsCode: [
    //     {
    //         required: isShowSmscode.value,
    //         message: '请输入验证码',
    //         trigger: 'change',
    //     },
    // ],
})

const dialogTitle = computed(() => {
    return props.type === 'add' ? '新增' : '编辑'
})

// const getCode = async () => {
//     const { mobile } = form || {}

//     if (!mobile) {
//         ElMessage.error('手机号码不能为空')
//         return
//     }

//     if (!phoneValidate(mobile.trim())) {
//         ElMessage.error('手机号码格式不正确')
//         return
//     }

//     if (countdown.value > 0 || querying.value) return // 防止重复点击

//     querying.value = true
//     console.log('请求验证码...')

//     let func

//     if (props.type === 'add') {
//         func = systemService.userSendAddUserCode
//     }

//     if (props.type === 'edit') {
//         func = systemService.userSendAuthMobileCode
//     }

//     if (!func) return

//     func({
//         mobile: mobile.trim(),
//     })
//         .then(() => {
//             console.log('验证码发送成功')
//             // 开始倒计时
//             countdown.value = 60
//             querying.value = false // 取消 loading
//             timer.value = setInterval(() => {
//                 countdown.value--
//                 if (countdown.value <= 0 && timer.value) {
//                     clearInterval(timer.value)
//                     timer.value = null
//                 }
//             }, 1000)
//         })
//         .catch((err) => {
//             console.log(err)
//             querying.value = false
//         })
// }

const handleUser = (user: IPageUserItem | null) => {
    if (!user) return
    const { username, nickname, orgId, role, status } = JSON.parse(JSON.stringify(user))

    if (props.type === 'edit') {
        // 处理 orgId：如果是数组，取第一个值；如果是字符串，直接使用
        if (Array.isArray(orgId) && orgId.length > 0) {
            form.value.orgId = orgId[0]
        } else if (typeof orgId === 'string') {
            form.value.orgId = orgId
        } else {
            form.value.orgId = ''
        }
        form.value.username = username
        form.value.nickname = nickname
        form.value.role = role
        form.value.password = '********'
        form.value.status = status.toString()
    }

    if (formRef.value) {
        formRef.value.clearValidate()
    }
}

const handleClosed = () => {
    resetForm()
}

const getRoleList = () => {
    queryingRole.value = true

    const params: IRoleListRequest = {}

    if (isPlatManager.value) {
        const { tenantId } = props.currentOrg || {}
        params.tenantId = tenantId
    }

    systemService
        .roleListByName(params)
        .then((res) => {
            queryingRole.value = false
            const { errCode, data } = res
            if (errCode === 0) {
                roleList.value = data
            } else {
                roleList.value = []
            }
        })
        .catch(() => {
            queryingRole.value = false
            roleList.value = []
        })
}

const onRoleFocus = () => {
    getRoleList()
}

const setDefaultOrg = () => {
    if (props.type === 'add') {
        const { id } = props.currentOrg || {}
        if (id) {
            form.value.orgId = id
        } else {
            form.value.orgId = ''
        }
    }
}

watch(
    () => props.user,
    (val) => {
        handleUser(val)
    }
)

watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
        if (val) {
            getRoleList()
            setDefaultOrg()
        }
    }
)
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="640"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose()"
        @closed="handleClosed"
    >
        <div class="user-update-dialog">
            <el-form ref="formRef" :rules="rules" :model="form">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="登录账号" label-position="top" prop="username">
                            <el-input
                                v-model="form.username"
                                clearable
                                autocomplete="off"
                                placeholder="6-12位英文和数字"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="登录密码" label-position="top" prop="password">
                            <el-input
                                v-model="form.password"
                                clearable
                                show-password
                                autocomplete="off"
                                readonly
                                onfocus="this.removeAttribute('readonly')"
                                placeholder="6-16位英文和数字，区分大小写"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属组织" label-position="top" prop="orgId">
                            <el-select
                                v-model="form.orgId"
                                collapse-tags
                                collapse-tags-tooltip
                                placeholder="请选择所属组织"
                                clearable
                            >
                                <el-option
                                    v-for="org in flatten(orgList)"
                                    :key="org.id"
                                    :label="org.name"
                                    :value="org.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否启用" label-position="top" prop="status">
                            <el-switch v-model="form.status" active-value="0" inactive-value="1" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="用户角色" label-position="top" prop="role">
                            <el-select
                                v-model="form.role"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                placeholder="请选择用户角色"
                                clearable
                                :loading="queryingRole"
                                @focus="onRoleFocus"
                            >
                                <el-option
                                    v-for="role in roleList"
                                    :key="role.id"
                                    :label="role.roleName"
                                    :value="role.roleId"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="联系人姓名" label-position="top" prop="nickname">
                            <el-input v-model="form.nickname" clearable placeholder="请输入联系人姓名" />
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item label="手机号" label-position="top" prop="mobile">
                            <el-input v-model="form.mobile" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="验证码" label-position="top" prop="smsCode" v-if="isShowSmscode">
                            <el-input v-model="form.smsCode" clearable>
                                <template #suffix>
                                    <el-button
                                        type="primary"
                                        link
                                        :disabled="countdown > 0"
                                        :loading="querying"
                                        @click="getCode"
                                    >
                                        {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}
                                    </el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col> -->
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose()">取消</el-button>
                <el-button type="primary" @click="save(formRef)" :loading="loading"> 提交 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.org-update-dialog :deep(.el-select__wrapper) {
    height: 45px;
    font-size: 14px;
}

.org-update-dialog :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
}
</style>
