<template>
    <div class="footer">
        <div class="copyright">{{  cDate }} {{ cName }}</div>
        <div class="version">{{ tcp }}</div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import PKG from '@/../package.json'
const currentYear = new Date().getFullYear()
const version = PKG.version
const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const oemStorage = sessionStorage.getItem('oemInfo')
const cDate = computed(() => {
    if(oemInfo?.value?.cDate){
        return oemInfo?.value?.cDate
    }else if(oemStorage && JSON.parse(oemStorage).cDate){
        return JSON.parse(oemStorage).cDate
    }else{
        return `2016-${currentYear}`
    }
})

const cName = computed(() => {
    if(oemInfo?.value?.cName){
        return oemInfo?.value?.cName
    }else if(oemStorage && JSON.parse(oemStorage).cName){
        return JSON.parse(oemStorage).cName
    }else{
        return '数族科技（南京）股份有限公司'
    }
})

const tcp = computed(() => {
    if(oemInfo?.value?.tcp){
        return `备案号：${oemInfo.value.tcp}`
    }else if(oemStorage && JSON.parse(oemStorage).tcp){
        return `备案号：${JSON.parse(oemStorage).tcp}`
    }else{
        return `版本号：${ version }`
    }
})

onMounted(() => {
    console.log('Footer mounted', oemInfo.value)
})

</script>

<style scoped>
.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 55px;
    z-index: 2;
    gap: 12px;
}

.footer div {
    font-size: var(--text-xl);
    color: var(--two-grey);
    font-weight: var(--weight-500);
}

@media screen and (max-width: 1600px) {
    .footer div {
        font-size: var(--text-base);
        line-height: var(--text-base);
    }
}

@media screen and (max-width: 1200px) {
    .footer div {
        font-size: var(--text-xs);
        line-height: var(--text-xs);
    }
}

@media screen and (max-width: 992px) {
    .footer div {
        font-size: 10px;
        line-height: 10px;
    }
}

@media screen and (max-width: 768px) {
    .footer div {
        font-size: 8px;
        line-height: 8px;
    }
}

@media screen and (max-width: 576px) {
    .footer div {
        font-size: 6px;
        line-height: 6px;
    }
}
</style>
